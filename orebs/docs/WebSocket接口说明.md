# WebSocket 协议文档

## 连接地址
```
ws://host:port/ws
```

## 消息格式

### 客户端请求消息
```json
{
  "id": "unique_request_id",
  "type": "操作类型",
  "token": "Bearer your_token_here"
}
```

### 服务端响应消息
```json
{
  "id": "对应的请求ID",
  "code": 0,
  "msg": "响应消息",
  "type": "操作类型"
}
```

### 系统通知消息
```json
{
  "type": "通知类型",
  "msg": "通知内容",
  "data": {
    // 可选的额外数据
  }
}
```

## 操作类型

### 1. init_oren - 初始化操作
**客户端请求:**
```json
{
  "id": "req_001",
  "type": "init_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_001",
  "code": 0,
  "msg": "OpenResty Edge Node 初始化成功",
  "type": "init_oren"
}
```

### 2. add_oren - 逐年设定时间（双向同步）
**客户端请求:**
```json
{
  "id": "req_002",
  "type": "add_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端立即响应:**
```json
{
  "id": "req_002",
  "code": 0,
  "msg": "已启动添加 OpenResty Edge Node 流程并实时通知进度",
  "type": "add_oren"
}
```

**服务端进度通知:**
```json
{
  "type": "add_oren_start",
  "msg": "开始逐年设定时间, 正在获取 NTP 时间...",
  "data": null
}
```

```json
{
  "type": "add_oren_calculated",
  "msg": "计算完成 - 将从 2024 年设定至 2025 年, 共 2 年",
  "data": {
    "start_year": 2024,
    "end_year": 2025,
    "total_years": 2
  }
}
```

```json
{
  "type": "add_oren_year_start",
  "msg": "开始处理 2024 年, 设定时间为: 2024-04-20 14:30:00",
  "data": {
    "year": 2024,
    "time": "2024-04-20 14:30:00"
  }
}
```

```json
{
  "type": "add_oren_year_complete",
  "msg": "2024 年处理已完成, 客户端可以执行相应操作.",
  "data": {
    "year": 2024,
    "completed_time": "2024-04-20 14:30:00",
    "services_restarted": true
  }
}
```

**客户端确认消息（必须）:**
```json
{
  "id": "ack_002_2024",
  "type": "add_oren_year_ack",
  "year": 2024
}
```

**最终完成通知:**
```json
{
  "id": "req_002",
  "code": 0,
  "msg": "所有年份的时间设定已完成",
  "type": "add_oren"
}
```

**重要说明:**
- 每年处理完成后，服务端会等待客户端发送确认消息（30秒超时）
- 客户端必须在收到 `add_oren_year_complete` 后发送对应年份的确认消息
- 如果超时或连接断开，服务端将自动执行 `start_oren` 逻辑并结束任务

### 3. start_oren - 启动操作（双向同步）
**客户端请求:**
```json
{
  "id": "req_003",
  "type": "start_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端立即响应:**
```json
{
  "id": "req_003",
  "code": 0,
  "msg": "已启动 OpenResty Edge Node 启动流程",
  "type": "start_oren"
}
```

**客户端确认消息（必须）:**
```json
{
  "id": "ack_003",
  "type": "start_oren_ack"
}
```

**服务端最终响应:**
```json
{
  "id": "req_003",
  "code": 0,
  "msg": "OpenResty Edge Node 启动成功",
  "type": "start_oren"
}
```

**重要说明:**
- 服务端执行完启动逻辑后会等待客户端确认（30秒超时）
- 客户端应在适当时机发送确认消息
- 超时或连接断开时，服务端会继续执行，不会报错

### 4. done_oren - 完成操作
**客户端请求:**
```json
{
  "id": "req_004",
  "type": "done_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_004",
  "code": 0,
  "msg": "OpenResty Edge Node 配置完成, 流程已结束.",
  "type": "done_oren"
}
```

**工作流完成通知:**
```json
{
  "type": "workflow_complete",
  "msg": "所有操作已完成",
  "data": null
}
```

## 系统通知类型

### 连接状态通知
- `connected` - 连接成功
- `waiting` - 排队等待
- `workflow_complete` - 整个工作流程完成

### add_oren 进度通知
- `add_oren_start` - 开始逐年设定
- `add_oren_calculated` - 计算完成
- `add_oren_year_start` - 开始处理某年
- `add_oren_year_complete` - 某年处理完成
- `add_oren_error` - 逐年设定过程中发生错误

### 客户端确认消息
- `add_oren_year_ack` - 客户端确认年份处理完成
- `start_oren_ack` - 客户端确认启动操作完成

## 重要说明

### 操作完成的定义
- **init_oren**: 同步操作，时间设定完成 + 服务重启完成后立即响应
- **add_oren**: 异步操作，每年处理完成后需要等待客户端确认才能继续下一年
- **start_oren**: 异步操作，执行完成后需要等待客户端确认
- **done_oren**: 同步操作，配置操作完成后立即响应

### 双向同步机制
- **add_oren**: 每年处理完成后等待客户端确认（30秒超时），超时或断开连接时自动执行 start_oren_logic 并结束任务
- **start_oren**: 执行完成后等待客户端确认（30秒超时），超时或断开连接时继续执行不报错
- **确认消息**: 客户端确认消息不需要 Token 验证，服务端收到后不发送响应

### 消息格式说明
- 所有消息内容使用中文逗号分隔，句号结尾
- 进度通知消息格式统一为："操作描述, 详细信息"
- 错误消息包含具体的错误原因和上下文信息
- 确认消息必须包含对应的年份信息（add_oren_year_ack）

### 时间延迟说明
- **add_oren**: 时间设定立即生效，服务重启完成后立即通知（无额外延迟）
- **start_oren**: 等待10秒确保NTP同步完成
- **done_oren**: 等待5秒确保配置发布生效

## 业务流程

1. **连接建立** → 收到 `connected` 通知
2. **初始化** → `init_oren` (同步: 时间设定 + 服务重启，Token验证在此步骤进行)
3. **逐年设定** → `add_oren` (异步: 每年处理完成后等待客户端确认，支持双向同步)
    - 每收到 `add_oren_year_complete` 通知后，客户端必须发送 `add_oren_year_ack` 确认
    - 30秒超时或连接断开时，服务端自动执行 `start_oren` 并结束任务
4. **启动服务** → `start_oren` (异步: NTP同步 + 10秒等待 + 服务重启，然后等待客户端确认)
    - 执行完成后等待客户端发送 `start_oren_ack` 确认
    - 30秒超时或连接断开时，服务端继续执行不报错
5. **完成配置** → `done_oren` (同步: 配置发布 + 5秒等待) → 收到 `workflow_complete` 通知
6. **断开连接**

**重要注意事项**:
- 除确认消息外，所有操作都需要Token验证
- 确认消息（`add_oren_year_ack`, `start_oren_ack`）不需要Token验证
- 双向同步机制确保客户端和服务端的操作协调一致
- 超时和断开连接有相应的容错处理机制

## 错误处理

当操作失败时，响应的 `code` 字段为非0值：
```json
{
  "id": "req_001",
  "code": 1,
  "msg": "Token 验证失败: Token 格式错误",
  "type": "init_oren"
}
```

**add_oren 操作错误示例:**
```json
{
  "type": "add_oren_error",
  "msg": "逐年设定时间过程中发生错误: 无法获取 NTP 时间",
  "data": {
    "error": "NTP 服务器连接失败"
  }
}
```

## 连接管理

- 同时只允许一个客户端连接
- 其他客户端会收到排队通知
- 当前客户端断开后，下一个排队的客户端会被激活